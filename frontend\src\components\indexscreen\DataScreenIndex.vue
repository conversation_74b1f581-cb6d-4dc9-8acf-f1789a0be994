<template>
  <div class="layout-container">
    <!-- 顶部标题栏 -->
    <header class="header-block">
      <h1 class="header-title">那洪街道智慧人大AI辅助履职平台</h1>
      <div class="header-right">
        <div class="time-display">
          <div class="date">{{ currentDate }}</div>
          <div class="time">{{ currentTime }}</div>
        </div>
        <button class="login-btn" @click="loginBackend">登录后台</button>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>

    <!-- 主体内容区域 -->
    <main class="main-content">
      <!-- 左侧面板 -->
      <aside class="left-sidebar">
        <!-- 代表层级统计 -->
        <div class="chart-block">
          <div class="tit"><span>代表层级统计</span></div>
          <div class="chart-content">
            <Chart1 :data="chartData.chart1?.data || []"></Chart1>
          </div>
        </div>

        <!-- 代表结构组成 -->
        <div class="chart-block">
          <div class="tit"><span>代表结构组成</span></div>
          <div class="chart-content">
            <Chart2 :data="chartData.chart2?.data || []"></Chart2>
          </div>
        </div>

        <!-- 意见建议 -->
        <div class="chart-block">
          <div class="tit"><span>意⻅建议</span></div>
          <div class="chart-content">
            <Chart4 :data="chartData.chart4?.data || []"></Chart4>
          </div>
        </div>
      </aside>

      <!-- 中间内容区 -->
      <div class="center-content">
        <!-- 主要内容区 - 地图 -->
        <section class="main-content-block no-border">
          <div class="chart-content">
            <Chart5
              :regions="chartData.chart5?.regions || {}"
              :districts="chartData.chart5?.districts || []"
            ></Chart5>
          </div>
        </section>

        <!-- 底部内容区 - 履职明细 -->
        <section class="bottom-content-block">
          <div class="tit"><span>履职明细</span></div>
          <div class="chart-content">
            <Chart6
              :selectedMonth="chartData.chart6?.selectedMonth || ''"
              :months="chartData.chart6?.months || []"
              :chartData="chartData.chart6?.chartData || {}"
            ></Chart6>
          </div>
        </section>
      </div>

      <!-- 右侧面板 -->
      <aside class="right-sidebar">
        <!-- 代表名单 -->
        <div class="chart-block">
          <div class="tit"><span>代表名单</span></div>
          <div class="chart-content">
            <Chart7 :rankingData="chartData.chart7?.rankingData || []"></Chart7>
          </div>
        </div>

        <!-- 履职统计 -->
        <div class="chart-block">
          <div class="tit"><span>履职统计</span></div>
          <div class="chart-content">
            <Chart8 :data="chartData.chart8?.data || []"></Chart8>
          </div>
        </div>

        <!-- AI知识库 -->
        <div class="chart-block">
          <div class="tit"><span>AI智能助手</span></div>
          <div class="chart-content">
            <Chart9 :data="chartData.chart9?.data || []"></Chart9>
          </div>
        </div>
      </aside>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import request from '@/api/http/client'
import Chart1 from './charts/Chart1.vue'
import Chart2 from './charts/Chart2.vue'
import Chart4 from './charts/Chart4.vue'
import Chart5 from './charts/Chart5.vue'
import Chart6 from './charts/Chart6.vue'
import Chart7 from './charts/Chart7.vue'
import Chart8 from './charts/Chart8.vue'
import Chart9 from './charts/Chart9.vue'

// 路由信息
const route = useRoute()

// 时间显示
const currentTime = ref('')
const currentDate = ref('')
let timer = null

// 数据状态
const chartData = ref({})
const loading = ref(true)

// 获取数据源配置
const getDataSourceConfig = () => {
  // 检查路由meta配置，默认使用API
  return route.meta?.useApi !== false
}

// 从静态文件获取数据
const fetchStaticData = async () => {
  const response = await fetch('/data.json')
  if (!response.ok) {
    throw new Error('Failed to fetch static data')
  }
  return await response.json()
}

// 从API获取数据
const fetchApiData = async () => {
  const response = await request({
    url: '/bigscreen/',
    method: 'get',
    showLoading: false  // 禁用全局Loading，使用页面自己的loading状态
  })
  return response.data
}

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    const useApi = getDataSourceConfig()
    console.log('数据源配置:', { useApi, route: route.name })

    let data
    if (useApi) {
      console.log('使用API数据源')
      data = await fetchApiData()
    } else {
      console.log('使用静态数据源')
      data = await fetchStaticData()
    }

    chartData.value = data
    console.log('数据加载成功:', data)
    console.log('🔍 Chart5数据详情:', data.chart5)
  } catch (error) {
    console.error('数据加载失败:', error)
    // 如果请求失败，尝试使用静态数据作为备用
    try {
      console.log('尝试使用静态数据作为备用')
      const fallbackData = await fetchStaticData()
      chartData.value = fallbackData
      console.log('备用数据加载成功')
    } catch (fallbackError) {
      console.error('备用数据加载也失败:', fallbackError)
      // 使用默认空数据
      chartData.value = {
        chart1: { data: [] },
        chart2: { data: [] },
        chart4: { data: [] },
        chart5: { regions: {}, districts: [] },
        chart6: { selectedMonth: '', months: [], chartData: {} },
        chart7: { rankingData: [] },
        chart8: { data: [] },
        chart9: { data: [] }
      }
    }
  } finally {
    loading.value = false
  }
}

const updateTime = () => {
  const now = new Date()
  currentDate.value = now
    .toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
    .replace(/\//g, '/')
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

const loginBackend = () => {
  // 在新标签页中打开登录页面
  window.open('/login', '_blank')
}

// 检查 Dify 聊天机器人状态
const checkDifyChatbot = () => {
  console.log('Checking Dify chatbot status...')
  console.log('Config exists:', !!window.difyChatbotConfig)
  console.log('Script exists:', !!document.getElementById('BcwlrHx1D4bYfJ2h'))

  // 检查聊天按钮是否已经渲染
  setTimeout(() => {
    const button = document.getElementById('dify-chatbot-bubble-button')
    console.log('Chatbot button found:', !!button)
    if (!button) {
      console.warn('Dify chatbot button not found. Please check the configuration.')
    }
  }, 3000) // 延长检查时间
}

onMounted(async () => {
  updateTime()
  timer = setInterval(updateTime, 1000)

  // 获取数据
  await fetchData()

  // 检查 Dify 聊天机器人状态（用于调试）
  setTimeout(() => {
    checkDifyChatbot()
  }, 2000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 注意：Dify 聊天机器人现在在 index.html 中全局加载，
  // 不需要在组件卸载时清理，因为它应该在整个应用中保持可用
})
</script>

<style scoped>
/* 整体容器样式 - 参考布局 */
.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: url('../../assets/bgImg.png') no-repeat center center;
  background-size: 105% 100%;
  font-family: 'Microsoft YaHei', sans-serif;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* 添加装饰效果 */
.layout-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(2, 166, 181, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(73, 188, 247, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 13, 74, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 确保内容在装饰层之上 */
.layout-container > * {
  position: relative;
  z-index: 1;
}

/* 顶部标题栏 */
.header-block {
  flex: 0 0 auto;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 改为右对齐，让右侧内容始终在最右边 */
  min-height: 80px;
  position: relative; /* 为绝对定位的标题提供定位上下文 */
}

.header-title {
  color: aliceblue;
  font-size: clamp(20px, 3vw, 32px);
  font-weight: bold;
  letter-spacing: 4px;
  text-shadow: 6px 7px 4px black;
  margin: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 5; /* 确保标题在背景之上，但在右侧内容之下 */
  pointer-events: none; /* 防止标题阻挡右侧内容的交互 */
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-left: auto; /* 确保始终在最右边 */
  z-index: 10; /* 确保在标题之上 */
}

.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: #5ecbff;
  font-family: 'Courier New', monospace;
  font-size: clamp(12px, 1.2vw, 16px);
  line-height: 1.2;
}

.date {
  font-size: clamp(14px, 1.4vw, 18px);
  margin-bottom: 2px;
  font-weight: 600;
}

.time {
  font-size: clamp(14px, 1.4vw, 18px);
  font-weight: 600;
}

.login-btn {
  background: transparent;
  border: 1px solid #5ecbff;
  border-radius: 6px;
  color: #5ecbff;
  padding: 0.5rem 1rem;
  font-size: clamp(12px, 1.2vw, 16px);
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Microsoft YaHei', sans-serif;
  white-space: nowrap;
}

.login-btn:hover {
  background: rgba(94, 203, 255, 0.1);
  box-shadow: 0 0 10px rgba(94, 203, 255, 0.3);
  transform: translateY(-1px);
}

.login-btn:active {
  transform: translateY(0) scale(0.98);
}

/* 主体内容区域 - 参考布局的三列结构 */
.main-content {
  display: flex;
  flex: 1;
  padding: 15px; /* 减少padding为内容留出更多空间 */
  gap: 8px;
  overflow: hidden;
}

/* 左侧边栏 - 28vw宽度，3个图表 */
.left-sidebar {
  display: flex;
  flex-direction: column;
  width: 28vw;
  gap: 8px;
  align-items: stretch; /* 确保所有子元素拉伸到相同宽度 */
}

/* 中间内容区 - flex: 1，上下两个区域 */
.center-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 4px; /* 减少gap，为内容留出更多空间 */
}

/* 右侧边栏 - 28vw宽度，3个图表 */
.right-sidebar {
  display: flex;
  flex-direction: column;
  width: 28vw;
  gap: 8px;
  align-items: stretch; /* 确保所有子元素拉伸到相同宽度 */
}

/* 主要内容区 - 地图区域，占中间列的大部分 */
.main-content-block {
  flex: 1 1 auto; /* 允许收缩，为履职明细让出空间 */
  min-height: 300px; /* 设置最小高度，确保地图可见 */
  background: transparent;
  border: 1px solid #5ecbff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: inset 0 0 18px 2px rgba(30, 144, 255, 0.5);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible; /* 允许标题文字显示在边框外 */
  padding: 0; /* 地图组件自己控制padding */
}

/* 无边框样式 - 用于地图区域 */
.main-content-block.no-border {
  border: none;
  box-shadow: none;
  margin-bottom: 0;
}

/* 底部内容区 - 履职明细，固定高度优先 */
.bottom-content-block {
  flex: 0 0 320px; /* 使用固定像素值，更精确控制 */
  min-height: 280px; /* 最小高度保护 */
  max-height: 400px; /* 最大高度限制 */
  background: transparent;
  border: 1px solid #5ecbff;
  border-radius: 10px;
  margin-bottom: 0; /* 移除底部margin，避免超出 */
  box-shadow: inset 0 0 18px 2px rgba(30, 144, 255, 0.5);
  padding: 0; /* 移除padding，由内容区域控制 */
  color: #ffffff;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: visible; /* 允许标题文字显示在边框外 */
}

/* 左右侧边栏的图表块 - 自动分配高度 */
.chart-block {
  flex: 1;
  background: transparent;
  border: 1px solid #5ecbff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: inset 0 0 18px 2px rgba(30, 144, 255, 0.5);
  padding: 0; /* 移除padding，由内容区域控制 */
  color: #ffffff;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: visible; /* 允许标题文字显示在边框外 */
  min-height: 0;
}

/* 移除最后一个chart-block的底部margin，确保底部对齐 */
.chart-block:last-child {
  margin-bottom: 0;
}

/* 悬浮效果保持原有样式 */
.chart-block:hover,
.bottom-content-block:hover {
  transform: translateY(-1px);
  box-shadow: inset 0 0 25px 3px rgba(30, 144, 255, 0.6);
  border-color: #5ecbff;
}

/* 地图区域不需要悬浮效果（因为没有边框） */

/* 标题样式 - 参考原始实现 */
.tit {
  color: rgb(202, 251, 251);
  margin-top: -10px;
  margin-left: 5px;
  font-size: clamp(12px, 1.2vw, 16px);
  font-weight: 500;
}

.tit span {
  background-color: rgb(1, 15, 60);
  letter-spacing: 2px;
  padding: 5px 6px 5px 8px;
  border-radius: 4px;
}

/* 图表内容区域 - 占满整个卡片 */
.chart-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 20px;
  padding-top: 15px; /* 减少顶部padding，因为标题现在不占用空间 */
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(94, 203, 255, 0.3);
  border-top: 3px solid #5ecbff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  color: #5ecbff;
  font-size: 18px;
  font-family: 'Microsoft YaHei', sans-serif;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 - 参考布局的断点 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 15px;
    gap: 15px;
  }

  .left-sidebar,
  .right-sidebar {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .chart-block {
    flex: 1 1 calc(50% - 8px);
    min-width: 200px;
  }

  .center-content {
    gap: 15px;
  }

  .bottom-content-block {
    flex: 0 0 280px; /* 中等屏幕使用固定高度 */
    min-height: 250px;
    max-height: 320px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px;
    gap: 10px;
  }

  .left-sidebar,
  .right-sidebar {
    flex-direction: column;
  }

  .chart-block {
    flex: none;
    min-width: auto;
  }

  .center-content {
    gap: 10px;
  }

  .bottom-content-block {
    flex: 0 0 240px; /* 小屏幕使用更小的固定高度 */
    min-height: 200px;
    max-height: 280px;
  }

  .header-block {
    padding: 10px;
    flex-direction: column;
    gap: 10px;
    justify-content: center; /* 小屏幕时居中对齐 */
  }

  .header-right {
    gap: 10px;
    margin-left: 0; /* 小屏幕时取消自动边距 */
    align-self: flex-end; /* 确保在小屏幕上也在右边 */
  }
}
</style>